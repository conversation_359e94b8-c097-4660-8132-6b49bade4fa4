#ifndef HW_S_MQTT_CALLBACK_BUSINESS_H
#define HW_S_MQTT_CALLBACK_BUSINESS_H

#include "../common.h"
#include "../utils/json.h"
#include "../utils/utils.h"
#include "../utils/serial.h"
#include "../utils/esp32_time.h"
#include "../utils/mqtt_s.h"
#include "../utils/ota.h"
#include "../databases/counter.h"
#include "../databases/keypad_database.h"
#include "../databases/fpm383_database.h"
#include "../databases/operation_database.h"
#include "../controls/sleep_control.h"
#include "../controls/fpm383_control.h"
#include "../controls/mqtt_control.h"
#include "unlock_business.h"

// MQTT 公有主题回调函数
void mqtt_callback_public();

// MQTT 专有主题回调函数
void mqtt_callback_self(const DynamicJsonDocument &doc);

#endif
