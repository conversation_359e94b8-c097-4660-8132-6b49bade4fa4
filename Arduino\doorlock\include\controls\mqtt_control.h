#ifndef HW_S_MQTT_CONTROL_H
#define HW_S_MQTT_CONTROL_H

#include "../common.h"
#include "../utils/utils.h"
#include "../utils/json.h"
#include "../utils/mqtt_s.h"
#include "../databases/utils.h"
#include "../databases/counter.h"
#include "../databases/operation_database.h"
#include <WiFi.h>

// MQTT 计数器报告
void mqtt_report_counter();

// MQTT 更新报告
void mqtt_report_update(int fx_id, bool state);

// MQTT 状态信息报告 & 请求下发数据
void mqtt_report_states();

// MQTT 操作记录报告
void mqtt_report_operation();

#endif
