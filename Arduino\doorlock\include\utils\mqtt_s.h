#ifndef HW_S_MQTT_S_H
#define HW_S_MQTT_S_H

#include "../common.h"
#include "utils.h"
#include "wifi_s.h"
#include "../databases/utils.h"
#include <WiFiClient.h>
#include <PubSubClient.h>

// 检查 MQTT 是否连接
bool mqtt_is_connected();

// 连接 MQTT 服务器
void try_connect_mqtt(MQTTConfig config, void (*mqtt_callback)(char *, uint8_t *, unsigned int));

// 发布 MQTT 信息
bool mqtt_publish(const char *topic, const char *message);

// 接收 MQTT 信息
bool mqtt_receive();

#endif
