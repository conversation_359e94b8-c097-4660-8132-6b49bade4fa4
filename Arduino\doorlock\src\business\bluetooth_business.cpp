#include "../../include/business/bluetooth_business.h"

// 当前会话加密类型
uint8_t session_type = 0xFF;
// 当前数据包随机数
uint32_t last_random_number = get_random_number() | 0x01;

// 获取密钥
bool get_key(String &key)
{
  bool status = false;
  if (session_type == 0x01)
  {
    status = get_super_password(key);
  }
  else if (session_type == 0x02)
  {
    status = get_admin_password(key);
  }
  return status;
}

// 蓝牙信息回复
void bluetooth_reply(int code, String name, int step = -1, int item = -1, DynamicJsonDocument *data_doc = nullptr)
{
  String key = "";
  bool ok = get_key(key);
  if (ok && key.length() == 16)
  {
    String data_json_string = "";
    if (data_doc != nullptr)
    {
      serializeJson(*data_doc, data_json_string);
    }
    DynamicJsonDocument doc(128 + data_json_string.length() + 17);
    doc["rand"] = last_random_number;
    doc["code"] = code;
    doc["name"] = name;
    doc["step"] = step;
    doc["item"] = item;
    doc["data"] = data_json_string;
    String json_string;
    serializeJson(doc, json_string);
    size_t json_length = json_string.length();
    uint8_t encrypted[json_length];
    uint8_t iv[16];
    generate_random_uint8(iv, 16);
    bool ok = aes128_ctr_encrypt((const uint8_t *)json_string.c_str(), json_length, (const uint8_t *)key.c_str(), iv, encrypted);
    if (ok)
    {
      size_t encrypted_length = json_length + 17;
      uint8_t encrypted_with_header[encrypted_length];
      encrypted_with_header[0] = session_type;
      memcpy(encrypted_with_header + 1, iv, 16);
      memcpy(encrypted_with_header + 17, encrypted, json_length);
      size_t packet_length = BLUETOOTH_MSG_SIZE - 4;
      int num_packets = std::ceil((double)encrypted_length / packet_length);
      String total_packets_hex;
      number_to_hex_string(num_packets, 2, total_packets_hex);
      for (int t = 0; t < num_packets; t++)
      {
        size_t start = t * packet_length;
        size_t chunk_size = std::min(packet_length, encrypted_length - start);
        String packet_data = "";
        for (size_t i = 0; i < chunk_size; i++)
        {
          packet_data += (char)encrypted_with_header[start + i];
        }
        String packet_index_hex;
        number_to_hex_string(t, 2, packet_index_hex);
        String complete_packet = total_packets_hex + packet_index_hex + packet_data;
        std::vector<uint8_t> buffer(complete_packet.begin(), complete_packet.end());
        bluetooth_notify(buffer.data(), buffer.size());
        delay(BLUETOOTH_PACKET_SEND_INTERVAL);
      }
    }
    else
    {
      print_log("bluetooth", "reply", "encryption failed");
    }
  }
  else
  {
    print_log("bluetooth", "reply", "no key was found");
  }
}

// 蓝牙任务保持连接（正常）
bool bluetooth_task_keep_connect_normal(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  set_sleep_timeout(SLEEP_TIMEOUT_NORMAL);
  sleep_report();
  return true;
}

// 蓝牙任务保持连接（短）
bool bluetooth_task_keep_connect_short(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  set_sleep_timeout(SLEEP_TIMEOUT_BLUETOOTH_SHORT);
  sleep_report();
  return true;
}

// 蓝牙任务保持连接（长）
bool bluetooth_task_keep_connect_long(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  set_sleep_timeout(SLEEP_TIMEOUT_BLUETOOTH_LONG);
  sleep_report();
  return true;
}

// 蓝牙任务设置设备模式
bool bluetooth_task_set_device_mode(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  if (!task["mode"].isNull())
  {
    int mode = task["mode"];
    bool status = set_device_mode(mode);
    return status;
  }
  return false;
}

// 蓝牙任务修改网络配置
bool bluetooth_task_modify_network_config(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  if (!task["wifi_id"].isNull() && !task["wifi_password"].isNull() && !task["mqtt_username"].isNull() && !task["mqtt_password"].isNull() && !task["mqtt_server"].isNull() && !task["custom_device_name"].isNull())
  {
    String wifi_id = task["wifi_id"];
    String wifi_password = task["wifi_password"];
    String mqtt_username = task["mqtt_username"];
    String mqtt_password = task["mqtt_password"];
    String mqtt_server = task["mqtt_server"];
    String custom_device_name = task["custom_device_name"];

    // 设置WiFi配置
    WiFiConfig wifi_config{wifi_id, wifi_password};
    bool wifi_status = set_wifi_config(wifi_config);

    // 设置MQTT配置
    MQTTConfig mqtt_config{mqtt_username, mqtt_password, mqtt_server};
    bool mqtt_status = set_mqtt_config(mqtt_config);

    // 设置设备名称
    bool device_name_status = ConfigurationDatabase::getInstance().update("custom_device_name", custom_device_name);
    if (device_name_status)
    {
      ConfigurationDatabase::getInstance().store();
    }

    return wifi_status && mqtt_status && device_name_status;
  }
  return false;
}

// 蓝牙任务新增密码
bool bluetooth_task_password_add(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  if (!task["id"].isNull() && !task["pwd"].isNull())
  {
    int id = task["id"];
    String pwd = task["pwd"];
    bool is_added = KeypadDatabase::getInstance().add(id, pwd);
    if (is_added)
    {
      KeypadDatabase::getInstance().store();
    }
    return is_added;
  }
  return false;
}

// 蓝牙任务删除密码
bool bluetooth_task_password_remove(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  if (!task["id"].isNull() && !task["pwd"].isNull())
  {
    int id = task["id"];
    String pwd = task["pwd"];
    bool is_removed = KeypadDatabase::getInstance().remove(id, pwd);
    if (is_removed)
    {
      KeypadDatabase::getInstance().store();
    }
    return is_removed;
  }
  return false;
}

// 蓝牙任务新增指纹
bool bluetooth_task_fpm383_add(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  if (!task["id"].isNull())
  {
    int id = task["id"];
    FPM383Status status = fpm383_register(id, fpm383_register_callback);
    if (status == FPM383Status::SUCCESS)
    {
      voice_write(VOICE_FPM383_OK, sizeof(VOICE_FPM383_OK), 0);
    }
    else
    {
      voice_write(VOICE_FPM383_ERROR, sizeof(VOICE_FPM383_ERROR), 0);
    }
    return status == FPM383Status::SUCCESS;
  }
  return false;
}

// 蓝牙任务删除指纹
bool bluetooth_task_fpm383_remove(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  if (!task["id"].isNull())
  {
    int id = task["id"];
    FPM383Status status = fpm383_remove(id);
    return status == FPM383Status::SUCCESS;
  }
  return false;
}

// 蓝牙任务获取超级管理员密码
bool bluetooth_task_get_super_password(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  int mode = get_device_mode();
  int count = Counter::getInstance().getCount("modify_password");
  if (mode == DEVICE_MODE_2 && count == 0)
  {
    String super_password;
    bool ok = get_super_password(super_password);
    if (ok)
    {
      reply["pwd"] = super_password;
    }
    return ok;
  }
  return false;
}

// 蓝牙任务修改管理员密码
bool bluetooth_task_modify_admin_password(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  if (!task["pwd"].isNull())
  {
    String pwd = task["pwd"];
    bool ok = pwd.length() == 16 && modify_admin_password(pwd);
    return ok;
  }
  return false;
}

// 蓝牙任务获取修改管理员密码次数
bool bluetooth_task_get_modify_admin_password_count(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  int count = Counter::getInstance().getCount("modify_password");
  reply["count"] = count;
  return true;
}

// 蓝牙任务更新 BluetoothNote
bool bluetooth_task_update_bluetooth_note(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  if (!task["note"].isNull())
  {
    String note = task["note"];
    if (note.length() <= 256)
    {
      FileManager fileManager(FILE_NAME_BLUETOOTH_NOTE, FILE_DEFAULT_BLUETOOTH_NOTE);
      bool ok = fileManager.write(note.c_str());
      return ok;
    }
  }
  return false;
}

// 蓝牙任务读取 BluetoothNote
bool bluetooth_task_get_bluetooth_note(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  FileManager fileManager(FILE_NAME_BLUETOOTH_NOTE, FILE_DEFAULT_BLUETOOTH_NOTE);
  String bluetooth_note;
  bool ok = fileManager.readString(bluetooth_note, fileManager.getSize());
  if (ok)
  {
    reply["note"] = bluetooth_note.substring(0, 256);
  }
  return ok;
}

// 蓝牙任务修改电机配置
bool bluetooth_task_modify_motor_config(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  if (!task["dir1"].isNull() && !task["dir2"].isNull() && !task["time1"].isNull() && !task["time2"].isNull() && !task["off"].isNull())
  {
    int dir1 = task["dir1"];
    int dir2 = task["dir2"];
    int time1 = task["time1"];
    int time2 = task["time2"];
    int off = task["off"];
    MotorConfig config{String(dir1), String(dir2), String(time1), String(time2), String(off)};
    bool ok = set_motor_config(config);
    return ok;
  }
  return false;
}

// 蓝牙任务转动电机
bool bluetooth_task_turn_motor(const JsonObjectConst &task, DynamicJsonDocument &reply)
{
  if (!task["uid"].isNull())
  {
    int uid = task["uid"];
    bool ok = on_unlock();
    if (ok)
    {
      OperationDatabase::getInstance().add(uid, OPERATION_TYPE_BLUETOOTH, esp32_get_time());
      OperationDatabase::getInstance().store();
    }
    return ok;
  }
  return false;
}

// 蓝牙任务逐项处理
bool bluetooth_task_item_process(const JsonObjectConst &task, DynamicJsonDocument &doc)
{
  bool status = false;
  String task_name = task["task"];
  if (task_name == "keep_normal")
  {
    status = bluetooth_task_keep_connect_normal(task, doc);
  }
  else if (task_name == "keep_short")
  {
    status = bluetooth_task_keep_connect_short(task, doc);
  }
  else if (task_name == "keep_long")
  {
    status = bluetooth_task_keep_connect_long(task, doc);
  }
  else if (task_name == "set_mode")
  {
    status = (session_type == 0x01) && bluetooth_task_set_device_mode(task, doc);
  }
  else if (task_name == "network_config")
  {
    status = bluetooth_task_modify_network_config(task, doc);
  }
  else if (task_name == "pwd_add")
  {
    status = bluetooth_task_password_add(task, doc);
  }
  else if (task_name == "pwd_remove")
  {
    status = bluetooth_task_password_remove(task, doc);
  }
  else if (task_name == "fpm_add")
  {
    status = bluetooth_task_fpm383_add(task, doc);
  }
  else if (task_name == "fpm_remove")
  {
    status = bluetooth_task_fpm383_remove(task, doc);
  }
  else if (task_name == "get_super_pwd")
  {
    status = bluetooth_task_get_super_password(task, doc);
  }
  else if (task_name == "modify_pwd")
  {
    status = (session_type == 0x01) && bluetooth_task_modify_admin_password(task, doc);
  }
  else if (task_name == "modify_pwd_count")
  {
    status = bluetooth_task_get_modify_admin_password_count(task, doc);
  }
  else if (task_name == "update_note")
  {
    status = bluetooth_task_update_bluetooth_note(task, doc);
  }
  else if (task_name == "get_note")
  {
    status = bluetooth_task_get_bluetooth_note(task, doc);
  }
  else if (task_name == "motor_config")
  {
    status = bluetooth_task_modify_motor_config(task, doc);
  }
  else if (task_name == "turn_motor")
  {
    status = bluetooth_task_turn_motor(task, doc);
  }
  else
  {
    status = false;
    print_log("bluetooth", "callback", "no matching task");
  }
  return status;
}

// 蓝牙任务列表处理
void bluetooth_task_list_process(const JsonArrayConst &task_list)
{
  for (size_t step = 0; step < task_list.size(); step++)
  {
    JsonArrayConst task_step = task_list[step];
    for (size_t item = 0; item < task_step.size(); item++)
    {
      JsonObjectConst task = task_step[item];
      size_t size = get_json_size(15, 4, 512);
      DynamicJsonDocument doc(size);
      if (!task["task"].isNull())
      {
        String task_name = task["task"];
        bool status = bluetooth_task_item_process(task, doc);
        if (status)
        {
          bluetooth_reply(BLUETOOTH_MSG_CODE_SUCCESS_STATUS, task_name, step, item, &doc);
        }
        else
        {
          bluetooth_reply(BLUETOOTH_MSG_CODE_ERROR_STATUS, task_name, step, item);
          break;
        }
      }
      else
      {
        print_log("bluetooth", "callback", "no task name");
        break;
      }
    }
  }
}

// 蓝牙数据分析处理
void bluetooth_message_analysis(std::string &message)
{
  uint8_t type = message.c_str()[0];
  session_type = type;
  String key = "";
  bool ok = get_key(key);
  if (ok && key.length() == 16)
  {
    uint8_t iv[16];
    memcpy(iv, message.c_str() + 1, 16);
    std::string message_body = message.substr(17);
    size_t message_body_length = message_body.length();
    uint8_t decrypted[message_body_length + 1];
    bool ok = aes128_ctr_decrypt((const uint8_t *)message_body.c_str(), message_body_length, (const uint8_t *)key.c_str(), iv, decrypted);
    if (ok)
    {
      decrypted[message_body_length] = '\0';
      String decrypted_str = String((char *)decrypted);
      size_t size = get_json_size(30, 10, decrypted_str.length());
      DynamicJsonDocument doc(size);
      DeserializationError error = deserializeJson(doc, decrypted_str);
      if (error)
      {
        print_log("bluetooth", "callback", "deserialization error");
      }
      else
      {
        if (!doc["rand"].isNull())
        {
          uint32_t random_number = doc["rand"];
          if (random_number == last_random_number + 1)
          {
            last_random_number = random_number;
            if (!doc["data"].isNull())
            {
              JsonArrayConst data = doc["data"];
              bluetooth_task_list_process(data);
            }
            else
            {
              print_log("bluetooth", "callback", "no data");
            }
          }
          else
          {
            last_random_number = get_random_number();
            bluetooth_reply(BLUETOOTH_MSG_CODE_HELLO, "_hello_");
            print_log("bluetooth", "callback", "reset random number");
          }
        }
        else
        {
          print_log("bluetooth", "callback", "no random number");
        }
      }
    }
    else
    {
      bluetooth_reply(BLUETOOTH_MSG_CODE_ERROR_AUTH, "_error_");
      print_log("bluetooth", "callback", "decryption failed");
    }
  }
  else
  {
    print_log("bluetooth", "callback", "key was not found");
  }
  finish_bluetooth_data_process();
  sleep_report();
}
