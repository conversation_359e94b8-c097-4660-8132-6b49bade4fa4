#include "../../include/business/unlock_business.h"

// 验证成功流程
bool on_unlock()
{
  MotorConfig config = get_motor_config();
  int direction_1 = config.direction_1.toInt();
  int direction_2 = config.direction_2.toInt();
  int time_1 = config.time_1.toInt();
  int time_2 = config.time_2.toInt();
  int off_time = config.off_time.toInt();
  bool ok = time_1 > 0 && time_2 > 0 && off_time > 0;
  if (ok)
  {
    time_1 = std::min(time_1, static_cast<int>(MOTOR_WORK_MAX_TIME));
    time_2 = std::min(time_2, static_cast<int>(MOTOR_WORK_MAX_TIME));
    off_time = std::min(off_time, static_cast<int>(UNLOCK_MAX_INTERVAL));
    turn_motor(direction_1 == 0, time_1);
    delay(off_time);
    // voice_write(VOICE_UNLOCK, sizeof(VOICE_UNLOCK), off_time);
    turn_motor(direction_2 == 0, time_2);
    // voice_write(VOICE_LOCK, sizeof(VOICE_LOCK), 500);
  }
  return ok;
}

// 验证失败流程
void on_error()
{
  voice_write(VOICE_ERROR, sizeof(VOICE_ERROR), 750);
}
