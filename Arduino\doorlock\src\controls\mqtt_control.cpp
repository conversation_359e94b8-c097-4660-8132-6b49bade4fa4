#include "../../include/controls/mqtt_control.h"

// 最近一次 MQTT 报告时间
unsigned long last_mqtt_report_time = 0;

// MQTT 计数器报告
void mqtt_report_counter()
{
  char mqtt_topic_self[50];
  get_mqtt_topic_self(mqtt_topic_self);
  String counter_string;
  Counter::getInstance().getAllCount(counter_string);
  mqtt_publish(mqtt_topic_self, counter_string.c_str());
}

// MQTT 更新报告
void mqtt_report_update(int fx_id, bool state)
{
  DynamicJsonDocument doc(512);
  JsonObject lock_received = doc.createNestedObject("lock_received");
  lock_received["fx_id"] = fx_id;
  if (state)
  {
    lock_received["state"] = "ok";
  }
  else
  {
    lock_received["state"] = "err";
  }
  String report_string;
  serializeJson(doc, report_string);
  mqtt_publish(MQTT_TOPIC_PUBLIC, report_string.c_str());
}

// MQTT 状态信息报告 & 请求下发数据
void mqtt_report_states()
{
  if (last_mqtt_report_time == 0 || get_time_diff(last_mqtt_report_time) > MQTT_REPORT_INTERVAL)
  {
    char entity_id[50];
    char device_name[50];
    char custom_device_name[50];
    char mqtt_topic_self[50];
    get_entity_id(entity_id);
    get_device_name(device_name);
    bool has_custom_device_name = get_custom_device_name(custom_device_name);
    get_mqtt_topic_self(mqtt_topic_self);

    float battery_percent = get_battery_percent(PIN_BATTERY, BATTERY_FULL_VALUE, BATTERY_EMPTY_VALUE);

    DynamicJsonDocument doc_status(1024);
    JsonObject info_status = doc_status.createNestedObject("lock_info");
    info_status["entity_id"] = entity_id;
    info_status["device_name"] = device_name;
    info_status["device_status"] = "on";
    info_status["electric_quantity"] = battery_percent;
    info_status["lock_topic"] = mqtt_topic_self;
    if (has_custom_device_name)
    {
      info_status["as_device_name"] = custom_device_name;
    }
    else
    {
      info_status["as_device_name"] = "";
    }
    info_status["ip_address"] = WiFi.localIP().toString();
    String status_string;
    serializeJson(doc_status, status_string);

    DynamicJsonDocument doc_action(512);
    JsonObject info_action = doc_action.createNestedObject("lock_action");
    info_action["lock_topic"] = mqtt_topic_self;
    info_action["msg"] = "ok";
    String action_string;
    serializeJson(doc_action, action_string);

    bool status_is_published = mqtt_publish(MQTT_TOPIC_PUBLIC, status_string.c_str());
    bool action_is_published = mqtt_publish(MQTT_TOPIC_PUBLIC, action_string.c_str());
    if (status_is_published && action_is_published)
    {
      last_mqtt_report_time = millis();
    }
  }
}

// MQTT 操作记录报告
void mqtt_report_operation()
{
  if (mqtt_is_connected())
  {
    for (int t = 0; t < 100; t++)
    {
      OperationRecord record;
      OperationDatabase::getInstance().get(0, record);
      int uid = record.uid;
      int type = record.type;
      time_t timestamp = record.timestamp;
      char mqtt_topic_self[50];
      get_mqtt_topic_self(mqtt_topic_self);
      if (OperationDatabase::getInstance().remove(0))
      {
        DynamicJsonDocument doc(512);
        JsonObject info_log = doc.createNestedObject("lock_log");
        info_log["f_id"] = uid;
        info_log["lock_topic"] = mqtt_topic_self;
        info_log["lock_action"] = "on";
        info_log["time"] = timestamp;
        if (type == OPERATION_TYPE_KEYPAD)
        {
          info_log["type"] = "pwd";
        }
        else if (type == OPERATION_TYPE_FPM383)
        {
          info_log["type"] = "fin";
        }
        String log_string;
        serializeJson(doc, log_string);
        mqtt_publish(MQTT_TOPIC_PUBLIC, log_string.c_str());
      }
      else
      {
        OperationDatabase::getInstance().store();
        break;
      }
    }
  }
}
