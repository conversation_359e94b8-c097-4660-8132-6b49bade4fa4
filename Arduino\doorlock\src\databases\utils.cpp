#include "../../include/databases/utils.h"

// 获取电机配置参数
MotorConfig get_motor_config()
{
  MotorConfig config{"0", "1", String(MOTOR_WORK_DEFAULT_TIME), String(MOTOR_WORK_DEFAULT_TIME), String(UNLOCK_DEFAULT_INTERVAL)};
  ConfigurationDatabase::getInstance().getConfig("motor_direction_1", config.direction_1);
  ConfigurationDatabase::getInstance().getConfig("motor_direction_2", config.direction_2);
  ConfigurationDatabase::getInstance().getConfig("motor_time_1", config.time_1);
  ConfigurationDatabase::getInstance().getConfig("motor_time_2", config.time_2);
  ConfigurationDatabase::getInstance().getConfig("motor_off_time", config.off_time);
  return config;
}

// 设置电机配置参数
bool set_motor_config(MotorConfig config)
{
  bool status = true;
  status = status && ConfigurationDatabase::getInstance().update("motor_direction_1", config.direction_1);
  status = status && ConfigurationDatabase::getInstance().update("motor_direction_2", config.direction_2);
  status = status && ConfigurationDatabase::getInstance().update("motor_time_1", config.time_1);
  status = status && ConfigurationDatabase::getInstance().update("motor_time_2", config.time_2);
  status = status && ConfigurationDatabase::getInstance().update("motor_off_time", config.off_time);
  if (status)
  {
    ConfigurationDatabase::getInstance().store();
  }
  return status;
}

// 获取设备名称（用户设置），custom_device_name 的长度建议至少为 50 位
bool get_custom_device_name(char *custom_device_name)
{
  String name;
  bool has_config = ConfigurationDatabase::getInstance().getConfig("custom_device_name", name);
  if (has_config)
  {
    sprintf(custom_device_name, "%s", name.c_str());
    return true;
  }
  else
  {
    return false;
  }
}

// 获取设备使用模式
int get_device_mode()
{
  String device_mode;
  bool has_config = ConfigurationDatabase::getInstance().getConfig("device_mode", device_mode);
  if (has_config && is_numeric_string(device_mode))
  {
    return device_mode.toInt();
  }
  else
  {
    return -1;
  }
}

// 设置设备使用模式
bool set_device_mode(int device_mode)
{
  initialize_all();
  bool status = ConfigurationDatabase::getInstance().update("device_mode", String(device_mode));
  ConfigurationDatabase::getInstance().store();
  return status;
}

// 获取超级管理员密码
bool get_super_password(String &password)
{
  String super_password;
  bool has_config = ConfigurationDatabase::getInstance().getConfig("super_password", super_password);
  if (!has_config)
  {
    generate_random_string(16, true, true, true, super_password);
    bool status = ConfigurationDatabase::getInstance().update("super_password", super_password);
    if (status)
    {
      ConfigurationDatabase::getInstance().store();
    }
    else
    {
      return false;
    }
  }
  int mode = get_device_mode();
  switch (mode)
  {
  case DEVICE_MODE_0:
    generate_random_string(16, true, true, true, password);
    break;
  case DEVICE_MODE_1:
    get_default_super_password(password);
    break;
  case DEVICE_MODE_2:
    password = super_password;
    break;
  default:
    generate_random_string(16, true, true, true, password);
    break;
  }
  return true;
}

// 获取管理员密码
bool get_admin_password(String &password)
{
  String admin_password;
  bool has_config = ConfigurationDatabase::getInstance().getConfig("admin_password", admin_password);
  if (has_config)
  {
    password = admin_password;
  }
  else
  {
    String default_super_password;
    get_default_super_password(default_super_password);
    uint64_t chip_id = ESP.getEfuseMac();
    uint8_t id_bytes[8];
    uint8_t iv[16];
    for (int t = 0; t < 8; t++)
    {
      uint8_t bit = (chip_id >> (t * 8)) & 0xFF;
      id_bytes[7 - t] = bit;
      iv[7 - t] = bit;
      iv[15 - t] = bit;
    }
    uint8_t encrypted[8];
    aes128_ctr_encrypt(id_bytes, 8, (const uint8_t *)default_super_password.c_str(), iv, encrypted);
    char hexStr[17];
    for (int t = 0; t < 8; t++)
    {
      sprintf(hexStr + t * 2, "%02X", encrypted[t]);
    }
    hexStr[16] = '\0';
    password = String(hexStr);
  }
  return true;
}

// 修改管理员密码
bool modify_admin_password(String &password)
{
  bool status = ConfigurationDatabase::getInstance().update("admin_password", password);
  ConfigurationDatabase::getInstance().store();
  if (status)
  {
    Counter::getInstance().add("modify_password");
    Counter::getInstance().store();
  }
  return status;
}
