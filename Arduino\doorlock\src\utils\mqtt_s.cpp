#include "../../include/utils/mqtt_s.h"

// MQTT 连接状态  0 = 未连接  1 = 已准备连接  2 = 已连接
unsigned short mqtt_connect_status = 0;

// WiFiClient
WiFiClient esp32_wifi_client;
// PubSubClient
PubSubClient esp32_mqtt_client(esp32_wifi_client);

// 检查 MQTT 是否连接
bool mqtt_is_connected()
{
  return esp32_mqtt_client.connected();
}

// 连接 MQTT 服务器
void try_connect_mqtt(MQTTConfig config, void (*mqtt_callback)(char *, uint8_t *, unsigned int))
{
  if (mqtt_connect_status == 0 && wifi_is_connected())
  {
    String mqtt_username = config.mqtt_username;
    String mqtt_password = config.mqtt_password;
    String mqtt_server = config.mqtt_server;
    if (!mqtt_username.isEmpty() && !mqtt_password.isEmpty() && !mqtt_server.isEmpty())
    {
      esp32_mqtt_client.setServer(mqtt_server.c_str(), mqtt_port);
      esp32_mqtt_client.setKeepAlive(3600);
      esp32_mqtt_client.setBufferSize(4096);
      esp32_mqtt_client.setCallback(mqtt_callback);
      esp32_mqtt_client.connect("esp32", mqtt_username.c_str(), mqtt_password.c_str());
    }
    else
    {
      print_log("mqtt", "init", "no mqtt config info!!");
    }
    mqtt_connect_status = 1;
  }
  if (mqtt_connect_status == 1 && mqtt_is_connected())
  {
    char mqtt_topic_self[50];
    get_mqtt_topic_self(mqtt_topic_self);
    esp32_mqtt_client.subscribe(MQTT_TOPIC_PUBLIC);
    esp32_mqtt_client.subscribe(mqtt_topic_self);
    mqtt_connect_status = 2;
  }
}

// 发布 MQTT 信息
bool mqtt_publish(const char *topic, const char *message)
{
  if (mqtt_is_connected())
  {
    esp32_mqtt_client.publish(topic, message);
    return true;
  }
  else
  {
    return false;
  }
}

// 接收 MQTT 信息
bool mqtt_receive()
{
  if (mqtt_is_connected())
  {
    esp32_mqtt_client.loop();
    return true;
  }
  else
  {
    return false;
  }
}
