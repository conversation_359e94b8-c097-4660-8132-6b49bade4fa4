#include "../../include/utils/wifi_s.h"

// 检查 WiFi 是否连接
bool wifi_is_connected()
{
  return WiFi.status() == WL_CONNECTED;
}

// 初始化 WiFi
void wifi_init(WiFiConfig config)
{
  String wifi_id = config.wifi_id;
  String wifi_password = config.wifi_password;
  if (!wifi_id.isEmpty() && !wifi_password.isEmpty())
  {
    WiFi.setTxPower(WIFI_POWER_2dBm);
    WiFi.begin(wifi_id.c_str(), wifi_password.c_str());
  }
  else
  {
    print_log("wifi", "init", "no wifi config info!!");
  }
}
